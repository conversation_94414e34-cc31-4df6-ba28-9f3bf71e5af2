<template>
  <!-- <div>
    {{ chatbotStyle }}
  </div>
  <div>
    {{ chatbotWindowStyle }}
  </div> -->
  <div
    :style="{
      ...chatbotStyle,
      zIndex: isOpen ? 0 : 10000,
    }"
    v-if="isBubbleVisible"
    class="chat-bubble-container"
  >
    <button
      v-if="!isOpen"
      @mousedown="startDrag"
      @touchstart="startDrag"
      type="button"
      :class="[
        avatar_url ? '' : `aiko-bg-${color_primary}-500 aiko-!hover:bg-${color_primary}-800`,
      ]"
      class="aiko-cursor-grab aiko-relative animate__animated animate__delay-0.5s animate__bounceIn chat-button aiko-text-white aiko-font-medium aiko-rounded-full aiko-p-0 aiko-text-center aiko-inline-flex aiko-items-center"
      :disabled="loadings['getBasicSettings']"
    >
      <div v-if="avatar_url" class="avatar-container">
        <div class="tooltip-container">
          <img
            :src="avatar_url"
            class="avatar-image"
            :style="{
              width: '96px',
              height: 'auto',
              transition: 'all 0.2s ease',
              animation: 'jump 1s infinite',
            }"
          />
          <div class="tooltip">{{ chatbot_name }}と話す</div>
        </div>
      </div>
      <Icon
        v-else
        icon="tabler:message-chatbot-filled"
        class="aiko-h-8 aiko-w-8 aiko-absolute"
        :style="{
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
        }"
      />
    </button>
  </div>
  <div
    class="aiko-relative aiko-chat-window aiko-rounded-xl aiko-overflow-hidden aiko-transition-all aiko-duration-300 aiko-overflow-y-hidden animate__animated animate__faster"
    :style="[
      chatbotWindowStyle,
      isOpen
        ? { height: isFullScreen ? '100vh' : getChatWindowHeight() + 'px' }
        : {
            height: '0px',
          },
    ]"
    :class="{
      'animate__bounceInUp vr-chat-window': isOpen && !isFullScreen,
      'animate__bounceOutDown aiko-h-0': !isOpen,
      'fullscreen-chat': isFullScreen,
    }"
    @mousedown="handleWindowMouseDown"
    @touchstart="handleWindowTouchStart"
  >
    <ChatBot> </ChatBot>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
// import { useChatStore } from '@/stores/chat' // Uncomment when needed
import { useAuthStore } from '@/stores/auth'
import { ref, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import ChatBot from '@/components/ChatBot.vue'
import { disableMobileZoom, enableMobileZoom } from '@/utils/metaViewport'
const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const { color_primary, isOpen, isBubbleVisible, isFullScreen, loadings, avatar_url, chatbot_name } =
  storeToRefs(settingsStore)
const { liffId } = storeToRefs(authStore)

const chatbotStyle = ref({
  position: 'fixed',
  left: window.innerWidth - (avatar_url ? 150 : 96) + 'px',
  top: window.innerHeight - (avatar_url ? 120 : 96) + 'px',
  transition: 'left 0.3s ease, top 0.3s ease',
}) as any

const chatbotWindowStyle = ref({
  position: 'fixed',
  left: window.innerWidth - 400 + 'px',
  top: window.innerHeight - 500 + 'px',
  transition: 'left 0.3s ease, top 0.3s ease',
  // height: '70vh',
}) as any

const windowStyle = ref({
  width: window.innerWidth,
  height: window.innerHeight,
}) as any

// Define functions for chat window dimensions
// Width of chat window, constrained to viewport
// Add extra space on the right (30px instead of 10px on the right side)
const getChatWindowWidth = () => Math.min(400, window.innerWidth - 40)
// Height of chat window (70vh or 500px, whichever is smaller) with extra padding for message box
const getChatWindowHeight = () => Math.min(669, window.innerHeight * 0.75, window.innerHeight - 20)

// Function to check if device is mobile or window is small
const isMobileOrSmallWindow = () => {
  // Check if window width is less than 768px (typical tablet breakpoint)
  // or check if it's a mobile device using userAgent
  return (
    window.innerWidth < 768 ||
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  )
}

// Initial value for CHAT_WINDOW_WIDTH
let CHAT_WINDOW_WIDTH = getChatWindowWidth()

// Get bubble size dynamically based on whether avatar_url exists
const getBubbleSize = () => {
  // When using avatar_url with width 96px, we need to ensure the bubble stays within screen boundaries
  if (avatar_url.value) {
    // When avatar_url exists, use the avatar width (96px) for boundary calculations
    // to ensure the avatar stays fully visible on screen
    return 96
  }
  return 48 // 12px * 4 (button size) - this is the fixed size of the button without avatar
}

onMounted(() => {
  // Set fullscreen mode by default if liffId has a value
  if (liffId.value) {
    settingsStore.isFullScreen = true
    settingsStore.isOpen = true

    // Disable zooming on mobile devices when in fullscreen mode
    disableMobileZoom()
  }

  window.addEventListener('resize', () => {
    windowStyle.value.width = window.innerWidth
    windowStyle.value.height = window.innerHeight

    // Update chat window width based on new viewport size
    CHAT_WINDOW_WIDTH = getChatWindowWidth()

    // Check if chat is open
    if (isOpen.value) {
      // Check if device is now mobile or window is small
      if (isMobileOrSmallWindow() || liffId.value) {
        // Switch to full screen mode
        isFullScreen.value = true

        // Position the chat window to cover the entire viewport
        chatbotWindowStyle.value.left = '0px'
        chatbotWindowStyle.value.top = '0px'
        chatbotWindowStyle.value.width = '100%'
        chatbotWindowStyle.value.height = '100%'

        // Disable scrolling on the body when in full screen mode
        document.body.style.overflow = 'hidden'
        return // Exit early, no need to update regular positioning
      } else {
        // Exit full screen mode if we're not on a mobile device
        isFullScreen.value = false
        document.body.style.overflow = ''
        chatbotWindowStyle.value.width = CHAT_WINDOW_WIDTH + 'px'
        chatbotWindowStyle.value.height = 'auto'
        chatbotWindowStyle.value.left = '0px'
        chatbotWindowStyle.value.top = '0px'
      }
    }

    // Only update position if not in full screen mode
    // Update chatbot position on window resize to keep it within bounds
    const currentLeft = parseFloat(chatbotStyle.value.left.replace('px', ''))
    const currentTop = parseFloat(chatbotStyle.value.top.replace('px', ''))

    // Get bubble size
    const bubbleSize = getBubbleSize()

    // Ensure chatbot stays within window boundaries
    if (currentLeft < 0) {
      chatbotStyle.value.left = '0px'
    } else if (currentLeft > window.innerWidth - bubbleSize) {
      chatbotStyle.value.left = window.innerWidth - bubbleSize + 'px'
    }

    if (currentTop < 0) {
      chatbotStyle.value.top = '0px'
    } else if (currentTop > window.innerHeight - bubbleSize) {
      chatbotStyle.value.top = window.innerHeight - bubbleSize + 'px'
    }

    // Ensure chat window stays within window boundaries
    const windowLeft = parseFloat(chatbotWindowStyle.value.left.replace('px', ''))
    const windowTop = parseFloat(chatbotWindowStyle.value.top.replace('px', ''))
    const windowChatHeight = getChatWindowHeight()

    // Check left boundary
    if (windowLeft < 0) {
      chatbotWindowStyle.value.left = '0px'
    }
    // Check right boundary
    else if (windowLeft > window.innerWidth - CHAT_WINDOW_WIDTH) {
      chatbotWindowStyle.value.left = window.innerWidth - CHAT_WINDOW_WIDTH + 'px'
    }

    // Check top boundary
    if (windowTop < 0) {
      chatbotWindowStyle.value.top = '0px'
    }
    // Check bottom boundary - add extra padding to ensure message box is fully visible
    else if (windowTop > window.innerHeight - windowChatHeight - 10) {
      // 10px extra padding
      chatbotWindowStyle.value.top = window.innerHeight - windowChatHeight - 10 + 'px'
    }

    // Update chat window position to be directly on top of the chat bubble
    const bubbleLeft = parseFloat(chatbotStyle.value.left.replace('px', ''))
    const bubbleTop = parseFloat(chatbotStyle.value.top.replace('px', ''))

    // Position the chat window directly on top of the bubble
    // Center the chat window horizontally relative to the bubble
    let idealLeft = bubbleLeft + bubbleSize / 2 - CHAT_WINDOW_WIDTH / 2

    // Ensure the chat window stays within the viewport horizontally
    idealLeft = Math.max(10, idealLeft) // Don't go beyond left edge (with 10px padding)
    // Add extra space on the right (30px instead of 10px)
    idealLeft = Math.min(window.innerWidth - CHAT_WINDOW_WIDTH - 30, idealLeft) // Don't go beyond right edge (with extra padding)
    chatbotWindowStyle.value.left = idealLeft + 'px'

    // Position the chat window at the same vertical position as the bubble
    // This makes it appear on top of the bubble
    let idealTop = bubbleTop + bubbleSize / 2 - getChatWindowHeight() / 2

    // Ensure the chat window stays within the viewport vertically
    idealTop = Math.max(10, idealTop) // Don't go beyond top edge (with 10px padding)
    idealTop = Math.min(window.innerHeight - getChatWindowHeight() - 10, idealTop) // Don't go beyond bottom edge (with 10px padding)
    chatbotWindowStyle.value.top = idealTop + 'px'
  })
})

const startDrag = (event: any) => {
  // Prevent default to avoid text selection during drag
  event.preventDefault()

  // Store initial click position to determine if it's a drag or a click
  const initialX = event.clientX
  const initialY = event.clientY
  let isDragging = false
  let hasMoved = false

  // Parse current position values
  const currentLeft = parseFloat(chatbotStyle?.value?.left?.replace('px', '')) || 0
  const currentTop = parseFloat(chatbotStyle?.value?.top?.replace('px', '')) || 0

  // Calculate offset from the point where user clicked
  const offsetX = event.clientX - currentLeft
  const offsetY = event.clientY - currentTop

  // Track velocity for inertia effect
  let lastX = event.clientX
  let lastY = event.clientY
  let velocityX = 0
  let velocityY = 0
  let lastTimestamp = performance.now()
  let animationFrameId: number | null = null

  const onMouseMove = (moveEvent: any) => {
    // Check if the mouse has moved enough to be considered a drag
    if (!hasMoved) {
      const dx = Math.abs(moveEvent.clientX - initialX)
      const dy = Math.abs(moveEvent.clientY - initialY)
      if (dx > 5 || dy > 5) {
        // 5px threshold for considering it a drag
        hasMoved = true
        isDragging = true
      }
    }

    // Calculate time delta for velocity
    const now = performance.now()
    const deltaTime = now - lastTimestamp
    lastTimestamp = now

    // Calculate new position
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const newLeft = moveEvent.clientX - offsetX
    const newTop = moveEvent.clientY - offsetY

    // Calculate velocity (pixels per millisecond)
    velocityX = (moveEvent.clientX - lastX) / (deltaTime || 1)
    velocityY = (moveEvent.clientY - lastY) / (deltaTime || 1)

    // Update last position
    lastX = moveEvent.clientX
    lastY = moveEvent.clientY

    // Use requestAnimationFrame for smoother updates
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }

    animationFrameId = requestAnimationFrame(() => {
      // Apply constraints to keep button inside viewport
      let constrainedLeft = newLeft
      let constrainedTop = newTop

      // Get bubble size
      const bubbleSize = getBubbleSize()

      // Constrain horizontal position to keep bubble fully visible
      if (constrainedLeft < 0) {
        constrainedLeft = 0
      } else if (constrainedLeft > windowWidth - bubbleSize) {
        constrainedLeft = windowWidth - bubbleSize
      }

      // Constrain vertical position to keep bubble fully visible
      if (constrainedTop < 0) {
        constrainedTop = 0
      } else if (constrainedTop > windowHeight - bubbleSize) {
        constrainedTop = windowHeight - bubbleSize
      }

      // Apply new position with smooth transition
      chatbotStyle.value.left = `${constrainedLeft}px`
      chatbotStyle.value.top = `${constrainedTop}px`

      // Add a subtle transition during drag for smoother movement
      chatbotStyle.value.transition = 'left 0.05s ease-out, top 0.05s ease-out'

      // Update chat window position to follow the chat bubble if it's open
      if (isOpen.value) {
        // Position the chat window directly on top of the bubble
        // Center the chat window horizontally relative to the bubble
        let idealLeft = constrainedLeft + bubbleSize / 2 - CHAT_WINDOW_WIDTH / 2

        // Ensure the chat window stays within the viewport horizontally
        idealLeft = Math.max(10, idealLeft) // Don't go beyond left edge (with 10px padding)
        // Add extra space on the right (30px instead of 10px)
        idealLeft = Math.min(window.innerWidth - CHAT_WINDOW_WIDTH - 30, idealLeft) // Don't go beyond right edge (with extra padding)
        chatbotWindowStyle.value.left = idealLeft + 'px'

        // Position the chat window at the same vertical position as the bubble
        // This makes it appear on top of the bubble
        let idealTop = constrainedTop + bubbleSize / 2 - getChatWindowHeight() / 2

        // Ensure the chat window stays within the viewport vertically
        idealTop = Math.max(10, idealTop) // Don't go beyond top edge (with 10px padding)
        idealTop = Math.min(window.innerHeight - getChatWindowHeight() - 10, idealTop) // Don't go beyond bottom edge (with 10px padding)
        chatbotWindowStyle.value.top = idealTop + 'px'

        chatbotWindowStyle.value.transition = 'left 0.05s ease-out, top 0.05s ease-out'
      }
    })
  }

  const applyInertia = () => {
    // Apply inertia effect after mouse is released
    if (Math.abs(velocityX) < 0.01 && Math.abs(velocityY) < 0.01) {
      // Stop animation when velocity is very low
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
        animationFrameId = null
      }
      // Reset transition when movement stops
      chatbotStyle.value.transition = ''
      return
    }

    // Get current position
    const currentLeft = parseFloat(chatbotStyle.value.left.replace('px', ''))
    const currentTop = parseFloat(chatbotStyle.value.top.replace('px', ''))

    // Calculate new position with inertia
    const newLeft = currentLeft + velocityX * 16 // 16ms is approx. one frame at 60fps
    const newTop = currentTop + velocityY * 16

    // Apply constraints
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    let constrainedLeft = newLeft
    let constrainedTop = newTop

    // Get bubble size
    const bubbleSize = getBubbleSize()

    // Constrain horizontal position to keep bubble fully visible
    if (constrainedLeft < 0) {
      constrainedLeft = 0
      velocityX = 0 // Stop horizontal movement at boundary
    } else if (constrainedLeft > windowWidth - bubbleSize) {
      constrainedLeft = windowWidth - bubbleSize
      velocityX = 0 // Stop horizontal movement at boundary
    }

    // Constrain vertical position to keep bubble fully visible
    if (constrainedTop < 0) {
      constrainedTop = 0
      velocityY = 0 // Stop vertical movement at boundary
    } else if (constrainedTop > windowHeight - bubbleSize) {
      constrainedTop = windowHeight - bubbleSize
      velocityY = 0 // Stop vertical movement at boundary
    }

    // Apply new position
    chatbotStyle.value.left = `${constrainedLeft}px`
    chatbotStyle.value.top = `${constrainedTop}px`

    // Update chat window position to follow the chat bubble if it's open
    if (isOpen.value) {
      // Position the chat window directly on top of the bubble
      // Center the chat window horizontally relative to the bubble
      let idealLeft = constrainedLeft + bubbleSize / 2 - CHAT_WINDOW_WIDTH / 2

      // Ensure the chat window stays within the viewport horizontally
      idealLeft = Math.max(10, idealLeft) // Don't go beyond left edge (with 10px padding)
      // Add extra space on the right (30px instead of 10px)
      idealLeft = Math.min(window.innerWidth - CHAT_WINDOW_WIDTH - 30, idealLeft) // Don't go beyond right edge (with extra padding)
      chatbotWindowStyle.value.left = idealLeft + 'px'

      // Position the chat window at the same vertical position as the bubble
      // This makes it appear on top of the bubble
      let idealTop = constrainedTop + bubbleSize / 2 - getChatWindowHeight() / 2

      // Ensure the chat window stays within the viewport vertically
      idealTop = Math.max(10, idealTop) // Don't go beyond top edge (with 10px padding)
      idealTop = Math.min(window.innerHeight - getChatWindowHeight() - 10, idealTop) // Don't go beyond bottom edge (with 10px padding)
      chatbotWindowStyle.value.top = idealTop + 'px'
    }

    // Reduce velocity (friction)
    velocityX *= 0.95
    velocityY *= 0.95

    // Continue animation
    animationFrameId = requestAnimationFrame(applyInertia)
  }

  // Stop dragging when mouse is released
  const stopDrag = () => {
    window.removeEventListener('mousemove', onMouseMove)
    window.removeEventListener('mouseup', stopDrag)

    // If it was just a click (not a drag), toggle the chat
    if (!isDragging && !hasMoved) {
      toggleChat()
      return
    }

    // Start inertia effect
    if (Math.abs(velocityX) > 0.05 || Math.abs(velocityY) > 0.05) {
      applyInertia()
    } else {
      // If velocity is too low, just reset transition
      chatbotStyle.value.transition = ''
    }
  }

  // Add event listeners for both mouse and touch events
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('mouseup', stopDrag)

  // Add touch event support
  window.addEventListener(
    'touchmove',
    (e) => {
      e.preventDefault() // Prevent scrolling
      if (e.touches.length > 0) {
        onMouseMove({
          clientX: e.touches[0].clientX,
          clientY: e.touches[0].clientY,
        })
      }
    },
    { passive: false },
  )

  window.addEventListener(
    'touchend',
    (e) => {
      // Prevent any default behavior
      e.preventDefault()
      stopDrag()
    },
    { passive: false },
  )
}

const startDragWindow = (event: any) => {
  // Check if the click is on an interactive element (textarea, input, button, etc.)
  const target = event.target as HTMLElement
  const isInteractive =
    ['TEXTAREA', 'INPUT', 'BUTTON', 'A', 'SELECT', 'OPTION', 'LABEL', 'VIDEO', 'AUDIO'].includes(
      target.tagName,
    ) ||
    target.closest('.message-bubble') ||
    target.closest('.message-box-container') ||
    target.closest('button') ||
    target.closest('.function-buttons') ||
    (window.getSelection() && window.getSelection()!.toString().length > 0)

  // Don't start drag if clicking on an interactive element
  if (isInteractive) {
    return
  }

  // Prevent default to avoid text selection during drag
  event.preventDefault()

  // Store initial click position to determine if it's a drag or a click
  const initialX = event.clientX
  const initialY = event.clientY
  let isDragging = false
  let hasMoved = false

  // Parse current position values
  const currentLeft = parseFloat(chatbotWindowStyle.value.left.replace('px', '')) || 0
  const currentTop = parseFloat(chatbotWindowStyle.value.top.replace('px', '')) || 0

  // Calculate offset from the point where user clicked
  const offsetX = event.clientX - currentLeft
  const offsetY = event.clientY - currentTop

  // Track velocity for inertia effect
  let lastX = event.clientX
  let lastY = event.clientY
  let velocityX = 0
  let velocityY = 0
  let lastTimestamp = performance.now()
  let animationFrameId: number | null = null

  // Drag and move in both directions
  const onMouseMove = (moveEvent: any) => {
    // Check if the mouse has moved enough to be considered a drag
    if (!hasMoved) {
      const dx = Math.abs(moveEvent.clientX - initialX)
      const dy = Math.abs(moveEvent.clientY - initialY || 0)
      if (dx > 5 || dy > 5) {
        // 5px threshold for considering it a drag
        hasMoved = true
        isDragging = true
      }
    }

    // Calculate time delta for velocity
    const now = performance.now()
    const deltaTime = now - lastTimestamp
    lastTimestamp = now

    // Calculate new position
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const newLeft = moveEvent.clientX - offsetX
    const newTop = moveEvent.clientY - offsetY

    // Calculate velocity (pixels per millisecond)
    velocityX = (moveEvent.clientX - lastX) / (deltaTime || 1)
    velocityY = (moveEvent.clientY - lastY) / (deltaTime || 1)

    // Update last position
    lastX = moveEvent.clientX
    lastY = moveEvent.clientY

    // Use requestAnimationFrame for smoother updates
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }

    animationFrameId = requestAnimationFrame(() => {
      // Apply constraints to keep window inside viewport
      let constrainedLeft = newLeft
      let constrainedTop = newTop
      const chatWindowHeight = getChatWindowHeight()

      // Constrain horizontal position
      if (constrainedLeft < 0) {
        constrainedLeft = 0
      } else if (constrainedLeft > windowWidth - 440) {
        constrainedLeft = windowWidth - 440
      }

      // Constrain vertical position
      if (constrainedTop < 0) {
        constrainedTop = 0
      } else if (constrainedTop > windowHeight - chatWindowHeight - 10) {
        // 10px padding
        constrainedTop = windowHeight - chatWindowHeight - 10
      }

      // Apply new position with smooth transition
      chatbotWindowStyle.value.left = `${constrainedLeft}px`
      chatbotWindowStyle.value.top = `${constrainedTop}px`

      // Add a subtle transition during drag for smoother movement
      chatbotWindowStyle.value.transition = 'left 0.05s ease-out, top 0.05s ease-out'
    })
  }

  const applyInertia = () => {
    // Apply inertia effect after mouse is released
    if (Math.abs(velocityX) < 0.01 && Math.abs(velocityY) < 0.01) {
      // Stop animation when velocity is very low
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
        animationFrameId = null
      }
      // Reset transition when movement stops
      chatbotWindowStyle.value.transition = ''
      return
    }

    // Get current position
    const currentLeft = parseFloat(chatbotWindowStyle.value.left.replace('px', ''))
    const currentTop = parseFloat(chatbotWindowStyle.value.top.replace('px', ''))

    // Calculate new position with inertia
    const newLeft = currentLeft + velocityX * 16 // 16ms is approx. one frame at 60fps
    const newTop = currentTop + velocityY * 16

    // Apply constraints
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const chatWindowHeight = getChatWindowHeight()
    let constrainedLeft = newLeft
    let constrainedTop = newTop

    // Constrain horizontal position
    if (constrainedLeft < 0) {
      constrainedLeft = 0
      velocityX = 0 // Stop horizontal movement at boundary
    } else if (constrainedLeft > windowWidth - 440) {
      constrainedLeft = windowWidth - 440
      velocityX = 0 // Stop horizontal movement at boundary
    }

    // Constrain vertical position
    if (constrainedTop < 0) {
      constrainedTop = 0
      velocityY = 0 // Stop vertical movement at boundary
    } else if (constrainedTop > windowHeight - chatWindowHeight - 10) {
      constrainedTop = windowHeight - chatWindowHeight - 10
      velocityY = 0 // Stop vertical movement at boundary
    }

    // Apply new position
    chatbotWindowStyle.value.left = `${constrainedLeft}px`
    chatbotWindowStyle.value.top = `${constrainedTop}px`

    // Reduce velocity (friction)
    velocityX *= 0.95
    velocityY *= 0.95

    // Continue animation
    animationFrameId = requestAnimationFrame(applyInertia)
  }

  // Stop dragging when mouse is released
  const stopDrag = () => {
    window.removeEventListener('mousemove', onMouseMove)
    window.removeEventListener('mouseup', stopDrag)

    // If it was just a click (not a drag), do nothing
    if (!isDragging && !hasMoved) {
      return
    }

    // Start inertia effect
    if (Math.abs(velocityX) > 0.05 || Math.abs(velocityY) > 0.05) {
      applyInertia()
    } else {
      // If velocity is too low, just reset transition
      chatbotWindowStyle.value.transition = ''
    }
  }

  // Add event listeners for both mouse and touch events
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('mouseup', stopDrag)

  // Add touch event support
  window.addEventListener(
    'touchmove',
    (e) => {
      e.preventDefault() // Prevent scrolling
      if (e.touches.length > 0) {
        onMouseMove({
          clientX: e.touches[0].clientX,
          clientY: e.touches[0].clientY,
        })
      }
    },
    { passive: false },
  )

  window.addEventListener(
    'touchend',
    (e) => {
      // Prevent any default behavior
      e.preventDefault()
      stopDrag()
    },
    { passive: false },
  )
}

// These functions can be used in future implementations if needed
// const resetChat = () => {
//   useChatStore().showConfirmReset = true
// }
//
// const closeChat = () => {
//   isOpen.value = false
// }

// Handler for mousedown event on chat window
const handleWindowMouseDown = (event: any) => {
  // Only allow dragging when not in fullscreen mode
  if (!isFullScreen.value) {
    startDragWindow(event)
  }
}

// Handler for touchstart event on chat window
const handleWindowTouchStart = (event: any) => {
  // Only allow dragging when not in fullscreen mode
  if (!isFullScreen.value) {
    startDragWindow(event)
  }
}

const toggleChat = () => {
  isOpen.value = !isOpen.value

  if (isOpen.value) {
    // Check if device is mobile or window is small
    if (isMobileOrSmallWindow() || liffId.value) {
      // Set to full screen mode on mobile or small window
      isFullScreen.value = true

      // Position the chat window to cover the entire viewport
      chatbotWindowStyle.value.left = '0px'
      chatbotWindowStyle.value.top = '0px'
      chatbotWindowStyle.value.width = '100%'
      chatbotWindowStyle.value.height = '100%'

      // Disable scrolling on the body when in full screen mode
      document.body.style.overflow = 'hidden'
    } else {
      // Regular positioning for desktop
      // Get current bubble position
      const bubbleLeft = parseFloat(chatbotStyle.value.left.replace('px', ''))
      const bubbleTop = parseFloat(chatbotStyle.value.top.replace('px', ''))
      const bubbleSize = getBubbleSize()

      // Position the chat window directly on top of the bubble
      // Center the chat window horizontally relative to the bubble
      let idealLeft = bubbleLeft + bubbleSize / 2 - CHAT_WINDOW_WIDTH / 2

      // Ensure the chat window stays within the viewport horizontally
      idealLeft = Math.max(10, idealLeft) // Don't go beyond left edge (with 10px padding)
      // Add extra space on the right (30px instead of 10px)
      idealLeft = Math.min(window.innerWidth - CHAT_WINDOW_WIDTH - 30, idealLeft) // Don't go beyond right edge (with extra padding)
      chatbotWindowStyle.value.left = idealLeft + 'px'

      // Position the chat window at the same vertical position as the bubble
      // This makes it appear on top of the bubble
      let idealTop = bubbleTop + bubbleSize / 2 - getChatWindowHeight() / 2

      // Ensure the chat window stays within the viewport vertically
      idealTop = Math.max(10, idealTop) // Don't go beyond top edge (with 10px padding)
      idealTop = Math.min(window.innerHeight - getChatWindowHeight() - 10, idealTop) // Don't go beyond bottom edge (with 10px padding)
      chatbotWindowStyle.value.top = idealTop + 'px'

      // Reset width and height to default values
      chatbotWindowStyle.value.width = CHAT_WINDOW_WIDTH + 'px'
      chatbotWindowStyle.value.height = 'auto'
    }
  } else {
    // When closing the chat, restore body scrolling if it was in full screen
    if (isFullScreen.value) {
      document.body.style.overflow = ''
      isFullScreen.value = false
    }
  }
}

// Watch for changes in isOpen
watch(
  () => isOpen.value,
  (newValue: any) => {
    if (newValue) {
      settingsStore.getBasicSettings()
    }
  },
)

// Watch for changes in isFullScreen
watch(
  () => isFullScreen.value,
  (newValue: any) => {
    if (newValue) {
      // Save the current position before going fullscreen
      chatbotWindowStyle.value._savedLeft = chatbotWindowStyle.value.left
      chatbotWindowStyle.value._savedTop = chatbotWindowStyle.value.top

      // Set fullscreen styles
      chatbotWindowStyle.value.left = '0px'
      chatbotWindowStyle.value.top = '0px'

      // Disable body scrolling
      document.body.classList.add('body-no-scroll')

      // Disable zooming on mobile devices
      disableMobileZoom()
    } else {
      // Restore the saved position when exiting fullscreen
      if (chatbotWindowStyle.value._savedLeft && chatbotWindowStyle.value._savedTop) {
        chatbotWindowStyle.value.left = chatbotWindowStyle.value._savedLeft
        chatbotWindowStyle.value.top = chatbotWindowStyle.value._savedTop
      }

      // Enable body scrolling
      document.body.classList.remove('body-no-scroll')

      // Re-enable zooming on mobile devices
      enableMobileZoom()
    }
  },
)
</script>

<style lang="scss">
.chat-bubble-container {
  position: fixed;
  width: 96px;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.aiko-chat-window {
  position: absolute;
  width: 400px;
  max-width: calc(100vw - 20px); /* Ensure it doesn't exceed viewport width minus padding */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.15);
  z-index: 9998; /* Lower than chat bubble to ensure bubble is visible */
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 2px; /* Add padding at the bottom to ensure message box is fully visible */
  cursor: move; /* Show move cursor */
  overflow: visible !important;
  border-radius: 12px;
  max-height: calc(100vh - 20px); /* Ensure it doesn't exceed viewport height minus padding */
  transition:
    width 0.3s ease,
    height 0.3s ease,
    left 0.3s ease,
    top 0.3s ease;
}

/* Dark mode styles for chat window */
.dark .aiko-chat-window {
  background: rgba(30, 30, 30, 0.85);
  border: 1px solid rgba(50, 50, 50, 0.5);
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.3);
  color: #f0f0f0;
}

/* Full screen mode styles */
.aiko-chat-window.fullscreen,
.fullscreen-chat {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  border-radius: 0 !important;
  border: none !important;
  z-index: 10001 !important; /* Higher than everything */
  background: rgba(255, 255, 255, 1) !important; /* Fully opaque in fullscreen */
}

/* Dark mode styles for fullscreen chat window */
.dark .aiko-chat-window.fullscreen,
.dark .fullscreen-chat {
  background: rgba(20, 20, 20, 1) !important; /* Dark background in fullscreen */
  color: #f0f0f0 !important;
}

/* Prevent text selection when dragging, but allow for specific elements */
.aiko-chat-window * {
  user-select: none;
}

/* Allow text selection and interaction in message content and inputs */
.aiko-chat-window .message-bubble,
.aiko-chat-window .message-bubble *,
.aiko-chat-window textarea,
.aiko-chat-window input {
  user-select: text !important;
  cursor: auto !important;
  pointer-events: auto !important;
}

.vr-chat-window {
  transform-style: preserve-3d;
  perspective: 1000px;
}
.chat-bubble-container {
  .chat-button {
    border-radius: 50%;
    cursor: grab;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
    width: 48px;
    height: 48px;
    padding: 0;
    position: relative;
    background-color: transparent;
  }

  .avatar-container {
    position: absolute;
    width: 96px;
    height: 96px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .tooltip-container {
    position: relative;
    display: inline-block;
  }

  .tooltip {
    visibility: hidden;
    position: absolute;
    background-color: white;
    color: #333;
    text-align: center;
    padding: 8px 20px;
    border-radius: 20px;
    z-index: 3;
    opacity: 0;
    transition: opacity 0.3s;
    white-space: nowrap;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

    /* Position the tooltip above the avatar but more to the left */
    bottom: 105%;
    left: -10%; /* Shifted more to the left from center */
    transform: translateX(-50%);
    margin-bottom: 12px;
  }

  /* Dark mode styles for tooltip */
  .dark .tooltip {
    background-color: #333;
    color: #f0f0f0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Add arrow to tooltip - shifted to the right */
  .tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 70%; /* Shifted to the right */
    margin-left: -5px;
    border-width: 10px;
    border-style: solid;
    border-color: white transparent transparent transparent;
  }

  /* Dark mode styles for tooltip arrow */
  .dark .tooltip::after {
    border-color: #333 transparent transparent transparent;
  }

  /* Show tooltip on hover */
  .tooltip-container:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }

  .avatar-image {
    width: 96px;
    height: auto;
    transition: transform 0.3s ease;
    display: block;
    max-width: 100%;
  }

  .chat-button:hover .avatar-image {
    transform: scale(1.05);
  }

  .chat-buttons-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .function-buttons {
    position: absolute;
    display: flex;
    flex-direction: row;
    gap: 8px;
    top: 40%; /* Position above the chat window */
    left: -20px;
    transform: translateX(-50%);
    z-index: 10000;
    pointer-events: auto; /* Ensure buttons can be clicked */
  }

  @keyframes float {
    0% {
      transform: translateY(0px) translateZ(0px) rotateX(0deg);
    }
    50% {
      transform: translateY(-5px) translateZ(5px) rotateX(1deg);
    }
    100% {
      transform: translateY(0px) translateZ(0px) rotateX(0deg);
    }
  }
  .chat-header {
    background: #007bff;
    color: white;
    padding: 10px;
    display: flex;
    justify-content: space-between;
  }
  .chat-body {
    padding: 10px;
  }

  .close-button {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.8;
  }

  .close-button:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  .fullscreen-chat {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    z-index: 10000 !important;
    animation: none !important;
    transform: none !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }

  /* Class to disable body scrolling when in fullscreen mode */
  .body-no-scroll {
    overflow: hidden !important;
    height: 100vh !important;
    width: 100vw !important;
    position: fixed !important;
    margin: 0 !important;
  }

  @keyframes jump {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0);
    }
  }
}

.llm-rag-chatbot {
  cursor: grab;
}
</style>
